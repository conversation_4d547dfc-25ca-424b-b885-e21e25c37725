import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen_l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('fr'),
  ];

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @dashboard.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// No description provided for @reception.
  ///
  /// In en, this message translates to:
  /// **'Reception'**
  String get reception;

  /// No description provided for @clients.
  ///
  /// In en, this message translates to:
  /// **'Clients'**
  String get clients;

  /// No description provided for @devices.
  ///
  /// In en, this message translates to:
  /// **'Devices'**
  String get devices;

  /// No description provided for @receiptList.
  ///
  /// In en, this message translates to:
  /// **'Receipt List'**
  String get receiptList;

  /// No description provided for @cashFlow.
  ///
  /// In en, this message translates to:
  /// **'Cash Flow'**
  String get cashFlow;

  /// No description provided for @expenses.
  ///
  /// In en, this message translates to:
  /// **'Expenses'**
  String get expenses;

  /// No description provided for @products.
  ///
  /// In en, this message translates to:
  /// **'Products'**
  String get products;

  /// No description provided for @technicians.
  ///
  /// In en, this message translates to:
  /// **'Technicians'**
  String get technicians;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @exitApp.
  ///
  /// In en, this message translates to:
  /// **'Confirm close'**
  String get exitApp;

  /// No description provided for @exitAppMsg.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to close this window ?'**
  String get exitAppMsg;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @new_add.
  ///
  /// In en, this message translates to:
  /// **'New'**
  String get new_add;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @phone_n.
  ///
  /// In en, this message translates to:
  /// **'Phone N°'**
  String get phone_n;

  /// No description provided for @device_type.
  ///
  /// In en, this message translates to:
  /// **'Device type'**
  String get device_type;

  /// No description provided for @brand.
  ///
  /// In en, this message translates to:
  /// **'Brand'**
  String get brand;

  /// No description provided for @serie.
  ///
  /// In en, this message translates to:
  /// **'Serie'**
  String get serie;

  /// No description provided for @model.
  ///
  /// In en, this message translates to:
  /// **'Model'**
  String get model;

  /// No description provided for @serial_n.
  ///
  /// In en, this message translates to:
  /// **'Serial N°'**
  String get serial_n;

  /// No description provided for @issue.
  ///
  /// In en, this message translates to:
  /// **'Issue(s)'**
  String get issue;

  /// No description provided for @deadline.
  ///
  /// In en, this message translates to:
  /// **'Deadline'**
  String get deadline;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'Day(s)'**
  String get days;

  /// No description provided for @estimated_price.
  ///
  /// In en, this message translates to:
  /// **'Estimated price'**
  String get estimated_price;

  /// No description provided for @remarks.
  ///
  /// In en, this message translates to:
  /// **'Remarks'**
  String get remarks;

  /// No description provided for @warranty.
  ///
  /// In en, this message translates to:
  /// **'Warranty'**
  String get warranty;

  /// No description provided for @emergency.
  ///
  /// In en, this message translates to:
  /// **'Emergency'**
  String get emergency;

  /// No description provided for @print_receipt.
  ///
  /// In en, this message translates to:
  /// **'Print receipt'**
  String get print_receipt;

  /// No description provided for @cancel_reception.
  ///
  /// In en, this message translates to:
  /// **'Cancel reception'**
  String get cancel_reception;

  /// No description provided for @close_after_printing.
  ///
  /// In en, this message translates to:
  /// **'Close after printing'**
  String get close_after_printing;

  /// No description provided for @id.
  ///
  /// In en, this message translates to:
  /// **'ID'**
  String get id;

  /// No description provided for @generate.
  ///
  /// In en, this message translates to:
  /// **'Barcode Generator'**
  String get generate;

  /// No description provided for @print_barcode_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Print Barcode'**
  String get print_barcode_tooltip;

  /// No description provided for @add_client_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Add new client'**
  String get add_client_tooltip;

  /// No description provided for @edit_client_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Edit selected client information'**
  String get edit_client_tooltip;

  /// No description provided for @select_client_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Select from existing clients list'**
  String get select_client_tooltip;

  /// No description provided for @add_device_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Add new device to the list'**
  String get add_device_tooltip;

  /// No description provided for @edit_device_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Edit selected device informations'**
  String get edit_device_tooltip;

  /// No description provided for @cancel_device_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Cancel editing'**
  String get cancel_device_tooltip;

  /// No description provided for @remove_device_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Remove device from the list'**
  String get remove_device_tooltip;

  /// No description provided for @timeline.
  ///
  /// In en, this message translates to:
  /// **'Timeline'**
  String get timeline;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @current_year.
  ///
  /// In en, this message translates to:
  /// **'Current year'**
  String get current_year;

  /// No description provided for @current_month.
  ///
  /// In en, this message translates to:
  /// **'Current month'**
  String get current_month;

  /// No description provided for @current_week.
  ///
  /// In en, this message translates to:
  /// **'Current week'**
  String get current_week;

  /// No description provided for @timeline_picker.
  ///
  /// In en, this message translates to:
  /// **'Timeline picker'**
  String get timeline_picker;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @no_filter.
  ///
  /// In en, this message translates to:
  /// **'Without filter'**
  String get no_filter;

  /// No description provided for @dealer.
  ///
  /// In en, this message translates to:
  /// **'Dealer'**
  String get dealer;

  /// No description provided for @particular.
  ///
  /// In en, this message translates to:
  /// **'Particular'**
  String get particular;

  /// No description provided for @company.
  ///
  /// In en, this message translates to:
  /// **'Company'**
  String get company;

  /// No description provided for @new_reception.
  ///
  /// In en, this message translates to:
  /// **'New reception'**
  String get new_reception;

  /// No description provided for @payment.
  ///
  /// In en, this message translates to:
  /// **'Payment'**
  String get payment;

  /// No description provided for @transactions.
  ///
  /// In en, this message translates to:
  /// **'transactions history'**
  String get transactions;

  /// No description provided for @user.
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get user;

  /// No description provided for @debts.
  ///
  /// In en, this message translates to:
  /// **'Debts'**
  String get debts;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @from.
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from;

  /// No description provided for @to.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// No description provided for @new_reception_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Create new reception for selected client'**
  String get new_reception_tooltip;

  /// No description provided for @delete_client_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Delete selected client'**
  String get delete_client_tooltip;

  /// No description provided for @client_payment_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Add payment for selected client'**
  String get client_payment_tooltip;

  /// No description provided for @transactions_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Manage selected client transactions history'**
  String get transactions_tooltip;

  /// No description provided for @phase.
  ///
  /// In en, this message translates to:
  /// **'Phase'**
  String get phase;

  /// No description provided for @phase_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Manage device phase'**
  String get phase_tooltip;

  /// No description provided for @technician.
  ///
  /// In en, this message translates to:
  /// **'Technician'**
  String get technician;

  /// No description provided for @technician_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Technician who worked on this device'**
  String get technician_tooltip;

  /// No description provided for @print_device.
  ///
  /// In en, this message translates to:
  /// **'Print'**
  String get print_device;

  /// No description provided for @print_device_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Print device owner receipt'**
  String get print_device_tooltip;

  /// No description provided for @situation.
  ///
  /// In en, this message translates to:
  /// **'Situation'**
  String get situation;

  /// No description provided for @situation_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Manage device owner situation'**
  String get situation_tooltip;

  /// No description provided for @delivery.
  ///
  /// In en, this message translates to:
  /// **'Delivery'**
  String get delivery;

  /// No description provided for @delivery_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Change device status to delivery stage'**
  String get delivery_tooltip;

  /// No description provided for @traceability.
  ///
  /// In en, this message translates to:
  /// **'Traceability'**
  String get traceability;

  /// No description provided for @traceability_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Selected device traceability'**
  String get traceability_tooltip;

  /// No description provided for @client.
  ///
  /// In en, this message translates to:
  /// **'Client'**
  String get client;

  /// No description provided for @on_hold.
  ///
  /// In en, this message translates to:
  /// **'On hold'**
  String get on_hold;

  /// No description provided for @diagnostic.
  ///
  /// In en, this message translates to:
  /// **'Diagnostic'**
  String get diagnostic;

  /// No description provided for @confirmation.
  ///
  /// In en, this message translates to:
  /// **'Confirmation'**
  String get confirmation;

  /// No description provided for @confirmed.
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// No description provided for @repaired.
  ///
  /// In en, this message translates to:
  /// **'Repaired'**
  String get repaired;

  /// No description provided for @not_repaired.
  ///
  /// In en, this message translates to:
  /// **'Not repaired'**
  String get not_repaired;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// No description provided for @parts_unavailable.
  ///
  /// In en, this message translates to:
  /// **'Part unavailable'**
  String get parts_unavailable;

  /// No description provided for @devices_at_workshop.
  ///
  /// In en, this message translates to:
  /// **'Devices at workshop'**
  String get devices_at_workshop;

  /// No description provided for @delivered_devices.
  ///
  /// In en, this message translates to:
  /// **'Delivered devices'**
  String get delivered_devices;

  /// No description provided for @cancel_delivery.
  ///
  /// In en, this message translates to:
  /// **'Cancel delivery'**
  String get cancel_delivery;

  /// No description provided for @cancel_delivery_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Cancel device delivery'**
  String get cancel_delivery_tooltip;

  /// No description provided for @edit_owner.
  ///
  /// In en, this message translates to:
  /// **'Edit owner'**
  String get edit_owner;

  /// No description provided for @edit_owner_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Change receipt owner (client)'**
  String get edit_owner_tooltip;

  /// No description provided for @delete_receipt.
  ///
  /// In en, this message translates to:
  /// **'Delete receipt'**
  String get delete_receipt;

  /// No description provided for @delete_receipt_tooltip.
  ///
  /// In en, this message translates to:
  /// **'Delete receipt permanently'**
  String get delete_receipt_tooltip;

  /// No description provided for @receipt_n.
  ///
  /// In en, this message translates to:
  /// **'Receipt N°'**
  String get receipt_n;

  /// No description provided for @can_not.
  ///
  /// In en, this message translates to:
  /// **'You can not do that :/'**
  String get can_not;

  /// No description provided for @select_device.
  ///
  /// In en, this message translates to:
  /// **'Please select a device first'**
  String get select_device;

  /// No description provided for @non.
  ///
  /// In en, this message translates to:
  /// **'non'**
  String get non;

  /// No description provided for @device_managment.
  ///
  /// In en, this message translates to:
  /// **'Device Phase Management'**
  String get device_managment;

  /// No description provided for @device_price.
  ///
  /// In en, this message translates to:
  /// **'Device Price'**
  String get device_price;

  /// No description provided for @est_to_final.
  ///
  /// In en, this message translates to:
  /// **'From estimated price to final price'**
  String get est_to_final;

  /// No description provided for @price.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// No description provided for @set.
  ///
  /// In en, this message translates to:
  /// **'Set'**
  String get set;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading'**
  String get loading;

  /// No description provided for @found.
  ///
  /// In en, this message translates to:
  /// **'found'**
  String get found;

  /// No description provided for @device_phases.
  ///
  /// In en, this message translates to:
  /// **'Device Phases'**
  String get device_phases;

  /// No description provided for @change.
  ///
  /// In en, this message translates to:
  /// **'Change'**
  String get change;

  /// No description provided for @sms_text.
  ///
  /// In en, this message translates to:
  /// **'SMS TEXT'**
  String get sms_text;

  /// No description provided for @auto_sms.
  ///
  /// In en, this message translates to:
  /// **'Auto SMS message...'**
  String get auto_sms;

  /// No description provided for @send_sms.
  ///
  /// In en, this message translates to:
  /// **'Send SMS'**
  String get send_sms;

  /// No description provided for @services.
  ///
  /// In en, this message translates to:
  /// **'Services'**
  String get services;

  /// No description provided for @enter.
  ///
  /// In en, this message translates to:
  /// **'Enter'**
  String get enter;

  /// No description provided for @and.
  ///
  /// In en, this message translates to:
  /// **'And'**
  String get and;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @exit.
  ///
  /// In en, this message translates to:
  /// **'Exit'**
  String get exit;

  /// No description provided for @final_price.
  ///
  /// In en, this message translates to:
  /// **'Final price'**
  String get final_price;

  /// No description provided for @priceUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Price updated successfully'**
  String get priceUpdatedSuccessfully;

  /// No description provided for @technicianAssignedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Technician assigned successfully'**
  String get technicianAssignedSuccessfully;

  /// No description provided for @phaseChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Phase changed to \"{phase}\" successfully'**
  String phaseChangedSuccessfully(String phase);

  /// No description provided for @remarksUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Remarks updated successfully'**
  String get remarksUpdatedSuccessfully;

  /// No description provided for @remarksSavedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Remarks saved successfully'**
  String get remarksSavedSuccessfully;

  /// No description provided for @failedToUpdatePrice.
  ///
  /// In en, this message translates to:
  /// **'Failed to update price'**
  String get failedToUpdatePrice;

  /// No description provided for @failedToUpdatePriceWithError.
  ///
  /// In en, this message translates to:
  /// **'Failed to update price: {error}'**
  String failedToUpdatePriceWithError(String error);

  /// No description provided for @failedToAssignTechnician.
  ///
  /// In en, this message translates to:
  /// **'Failed to assign technician'**
  String get failedToAssignTechnician;

  /// No description provided for @failedToAssignTechnicianWithError.
  ///
  /// In en, this message translates to:
  /// **'Failed to assign technician: {error}'**
  String failedToAssignTechnicianWithError(String error);

  /// No description provided for @failedToChangePhase.
  ///
  /// In en, this message translates to:
  /// **'Failed to change phase'**
  String get failedToChangePhase;

  /// No description provided for @failedToChangePhaseWithError.
  ///
  /// In en, this message translates to:
  /// **'Failed to change phase: {error}'**
  String failedToChangePhaseWithError(String error);

  /// No description provided for @failedToSaveRemarks.
  ///
  /// In en, this message translates to:
  /// **'Failed to save remarks'**
  String get failedToSaveRemarks;

  /// No description provided for @failedToSendSms.
  ///
  /// In en, this message translates to:
  /// **'Failed to send SMS: {error}'**
  String failedToSendSms(String error);

  /// No description provided for @sendSms.
  ///
  /// In en, this message translates to:
  /// **'Send SMS'**
  String get sendSms;

  /// No description provided for @smsTemplateLoadedMessage.
  ///
  /// In en, this message translates to:
  /// **'An SMS template has been loaded for this phase. Would you like to send the SMS notification to the client now?'**
  String get smsTemplateLoadedMessage;

  /// No description provided for @smsMessage.
  ///
  /// In en, this message translates to:
  /// **'SMS Message'**
  String get smsMessage;

  /// No description provided for @smsError.
  ///
  /// In en, this message translates to:
  /// **'SMS Error'**
  String get smsError;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @technicianWillBe.
  ///
  /// In en, this message translates to:
  /// **'Technician will be: \"{technician}\"'**
  String technicianWillBe(String technician);

  /// No description provided for @currentTechnician.
  ///
  /// In en, this message translates to:
  /// **'Current technician: {technician}'**
  String currentTechnician(String technician);

  /// No description provided for @phaseSuccessfullyChanged.
  ///
  /// In en, this message translates to:
  /// **'Phase successfully changed to \"{phase}\"'**
  String phaseSuccessfullyChanged(String phase);

  /// No description provided for @phaseWillChange.
  ///
  /// In en, this message translates to:
  /// **'Phase will change from \"{oldPhase}\" to \"{newPhase}\"'**
  String phaseWillChange(String oldPhase, String newPhase);

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @unknown.
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get unknown;

  /// No description provided for @notAssigned.
  ///
  /// In en, this message translates to:
  /// **'Not assigned'**
  String get notAssigned;

  /// No description provided for @smsSentSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'SMS sent successfully'**
  String get smsSentSuccessfully;

  /// No description provided for @failedToSendSmsViaFrontline.
  ///
  /// In en, this message translates to:
  /// **'Failed to send SMS via Frontline'**
  String get failedToSendSmsViaFrontline;

  /// No description provided for @smsServiceTimeout.
  ///
  /// In en, this message translates to:
  /// **'SMS service timeout: {message}'**
  String smsServiceTimeout(String message);

  /// No description provided for @frontlineSmsNotConfigured.
  ///
  /// In en, this message translates to:
  /// **'Frontline SMS not configured'**
  String get frontlineSmsNotConfigured;

  /// No description provided for @connectionTimedOut.
  ///
  /// In en, this message translates to:
  /// **'Connection timed out'**
  String get connectionTimedOut;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
    case 'fr':
      return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
